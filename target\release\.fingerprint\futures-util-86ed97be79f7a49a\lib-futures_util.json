{"rustc": 17690870240741347250, "features": "[]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 8541531392191447418, "path": 8628501702731265679, "deps": [[1615478164327904835, "pin_utils", false, 4031594554817608828], [1906322745568073236, "pin_project_lite", false, 4871519555512407100], [7620660491849607393, "futures_core", false, 9353799322333569453], [16240732885093539806, "futures_task", false, 913217832498451274]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-util-86ed97be79f7a49a\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}