{"rustc": 17690870240741347250, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"any_all_workaround\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"serde\", \"simd-accel\"]", "target": 17616512236202378241, "profile": 16503403049695105087, "path": 10814409497664984566, "deps": [[2828590642173593838, "cfg_if", false, 8316091941523251432]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\encoding_rs-bdbe38b28f2dcb89\\dep-lib-encoding_rs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}