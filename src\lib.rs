use std::sync::Arc;
use parking_lot::RwLock;
use samp::plugin::SampPlugin;
use samp::{initialize_plugin, native};
use samp::amx::Amx;
use samp::error::AmxResult;
use log::{info, error, warn};
use tokio::runtime::Runtime;

mod config;
mod monitor;
mod webhook;

use config::Config;
use monitor::{ResourceMonitor, ServerMetrics};
use webhook::WebhookClient;


struct SampResourceMonitor {
    metrics: Arc<RwLock<ServerMetrics>>,
    webhook_client: Option<WebhookClient>,
    monitor: ResourceMonitor,
    _runtime: Arc<Runtime>,
}

impl SampResourceMonitor {
    fn new() -> Self {
        Self::setup_logging();

        info!("Initializing Resource Monitor Plugin v1.0.1");

        let config = match Config::load_from_file("config/resource_monitor.json") {
            Ok(config) => {
                if let Err(e) = config.validate() {
                    error!("Configuration validation failed: {}", e);
                    warn!("Using default configuration");
                    Config::default()
                } else {
                    config
                }
            }
            Err(e) => {
                error!("Failed to load configuration: {}", e);
                warn!("Using default configuration");
                Config::default()
            }
        };

        info!("Configuration loaded: webhook_enabled={}, interval={}s", 
              config.enable_webhook, config.interval_seconds);

        let runtime = Arc::new(
            Runtime::new().expect("Failed to create Tokio runtime")
        );

        let mut monitor = ResourceMonitor::new(config.clone());
        let metrics = monitor.start_monitoring();

        let webhook_client = if config.enable_webhook {
            Some(WebhookClient::new(config.clone()))
        } else {
            None
        };

        if let Some(ref webhook_client) = webhook_client {
            webhook_client.start_periodic_push(Arc::clone(&metrics));
        }

        info!("SA-MP Resource Monitor Plugin initialized successfully");

        Self {
            metrics: Arc::clone(&metrics),
            webhook_client,
            monitor,
            _runtime: runtime,
        }
    }

    fn setup_logging() {
        let _ = fern::Dispatch::new()
            .level(log::LevelFilter::Info)
            .format(|callback, message, record| {
                callback.finish(format_args!(
                    "[{}] [{}] {}",
                    chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC"),
                    record.level(),
                    message
                ))
            })
            .chain(std::io::stdout())
            .apply();
    }
}

impl SampPlugin for SampResourceMonitor {
    fn on_load(&mut self) {
        info!("Resource Monitor Plugin loaded (v1.0.1)");
    
        let metrics = self.metrics.read();
        info!("Initial metrics - CPU: {:.2}%, RAM: {:.2}MB, TPS: {:.2}",
              metrics.cpu, metrics.ram, metrics.tps);
    }

    fn on_unload(&mut self) {
        info!("Resource Monitor Plugin unloaded");
    }

    fn process_tick(&mut self) {        self.monitor.tick();
    }
}

impl SampResourceMonitor {
    /// Native: Float:GetServerCPU()
    #[native(name = "GetServerCPU")]
    pub fn get_server_cpu(&mut self, _amx: &Amx) -> AmxResult<f32> {
        let cpu_usage = self.metrics.read().cpu;
        Ok(cpu_usage)
    }

    /// Native: Float:GetServerRAM()
    #[native(name = "GetServerRAM")]
    pub fn get_server_ram(&mut self, _amx: &Amx) -> AmxResult<f32> {
        let ram_usage = self.metrics.read().ram;
        Ok(ram_usage)
    }

    /// Native: Float:GetTickRate()
    #[native(name = "GetTickRate")]
    pub fn get_tick_rate(&mut self, _amx: &Amx) -> AmxResult<f32> {
        let tick_rate = self.metrics.read().tps;
        Ok(tick_rate)
    }

    /// Native: bool:PushServerMetrics()
    #[native(name = "PushServerMetrics")]
    pub fn push_server_metrics(&mut self, _amx: &Amx) -> AmxResult<bool> {
        if let Some(webhook_client) = &self.webhook_client {
            let current_metrics = self.metrics.read().clone();

            let result = self._runtime.block_on(async {
                webhook_client.send_metrics_manual(&current_metrics).await
            });

            match result {
                Ok(success) => Ok(success),
                Err(_) => Ok(false),
            }
        } else {
            Ok(false)
        }
    }

    /// Native: GetServerMetricsEx(&Float:cpu, &Float:ram, &Float:tps)
    #[native(name = "GetServerMetricsEx")]
    pub fn get_server_metrics_ex(
        &mut self,
        _amx: &Amx,
        cpu: &mut f32,
        ram: &mut f32,
        tps: &mut f32,
    ) -> AmxResult<i32> {
        let metrics = self.metrics.read();
        *cpu = metrics.cpu;
        *ram = metrics.ram;
        *tps = metrics.tps;
        Ok(1)
    }

    /// Native: bool:IsServerUnderLoad()
    #[native(name = "IsServerUnderLoad")]
    pub fn is_server_under_load(&mut self, _amx: &Amx) -> AmxResult<bool> {
        let metrics = self.metrics.read();
        let under_load = metrics.cpu > 80.0 || metrics.tps < 40.0;
        Ok(under_load)
    }
}

initialize_plugin!(
    natives: [
        SampResourceMonitor::get_server_cpu,
        SampResourceMonitor::get_server_ram,
        SampResourceMonitor::get_tick_rate,
        SampResourceMonitor::push_server_metrics,
        SampResourceMonitor::get_server_metrics_ex,
        SampResourceMonitor::is_server_under_load,
    ],
    {
        samp::plugin::enable_process_tick();
        SampResourceMonitor::new()
    }
);
