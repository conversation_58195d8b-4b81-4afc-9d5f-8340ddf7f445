use std::sync::Arc;
use std::time::Duration;
use parking_lot::RwLock;
use reqwest::Client;
use tokio::time::interval;
use log::{info, warn, error, debug};
use serde_json::json;
use anyhow::{Result, Context};

use crate::monitor::ServerMetrics;
use crate::config::Config;

pub struct WebhookClient {
    client: Client,
    config: Config,
}

impl WebhookClient {
    pub fn new(config: Config) -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(10))
            .user_agent("samp-resource-monitor/0.1.0")
            .build()
            .expect("Failed to create HTTP client");

        Self { client, config }
    }

    pub async fn send_metrics(&self, metrics: &ServerMetrics) -> Result<()> {
        if !self.config.enable_webhook || self.config.webhook_url.is_empty() {
            return Ok(());
        }

        let payload = json!({
            "cpu": metrics.cpu,
            "ram": metrics.ram,
            "tps": metrics.tps,
            "timestamp": metrics.timestamp.to_rfc3339()
        });

        debug!("Sending metrics to webhook: {}", payload);

        let response = self.client
            .post(&self.config.webhook_url)
            .json(&payload)
            .send()
            .await
            .context("Failed to send webhook request")?;

        if response.status().is_success() {
            debug!("Webhook sent successfully: {}", response.status());
        } else {
            warn!("Webhook failed with status: {}", response.status());
            let error_text = response.text().await.unwrap_or_default();
            if !error_text.is_empty() {
                warn!("Webhook error response: {}", error_text);
            }
        }

        Ok(())
    }

    pub async fn send_metrics_manual(&self, metrics: &ServerMetrics) -> Result<bool> {
        match self.send_metrics(metrics).await {
            Ok(()) => {
                info!("Manual metrics push successful");
                Ok(true)
            }
            Err(e) => {
                error!("Manual metrics push failed: {}", e);
                Ok(false)
            }
        }
    }

    pub fn start_periodic_push(&self, metrics: Arc<RwLock<ServerMetrics>>) {
        if !self.config.enable_webhook {
            info!("Webhook is disabled, skipping periodic push");
            return;
        }

        let client = self.client.clone();
        let webhook_url = self.config.webhook_url.clone();
        let interval_duration = Duration::from_secs(self.config.interval_seconds);

        tokio::spawn(async move {
            let mut interval = interval(interval_duration);
            info!("Starting periodic webhook push every {} seconds", interval_duration.as_secs());

            loop {
                interval.tick().await;

                if webhook_url.is_empty() {
                    continue;
                }

                let current_metrics = metrics.read().clone();
                
                let payload = json!({
                    "cpu": current_metrics.cpu,
                    "ram": current_metrics.ram,
                    "tps": current_metrics.tps,
                    "timestamp": current_metrics.timestamp.to_rfc3339()
                });

                match client.post(&webhook_url).json(&payload).send().await {
                    Ok(response) => {
                        if response.status().is_success() {
                            debug!("Periodic webhook sent successfully");
                        } else {
                            warn!("Periodic webhook failed with status: {}", response.status());
                        }
                    }
                    Err(e) => {
                        error!("Failed to send periodic webhook: {}", e);
                    }
                }
            }
        });
    }
}


