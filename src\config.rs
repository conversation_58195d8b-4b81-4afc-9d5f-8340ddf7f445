use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;
use anyhow::{Result, Context};
use log::{info, warn};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub webhook_url: String,
    pub interval_seconds: u64,
    pub enable_webhook: bool,
    pub enable_logging: bool,
    pub log_file: String,
    pub cpu_warning_threshold: f32,
    pub tps_warning_threshold: f32,
    pub ram_warning_threshold: f32,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            webhook_url: String::new(),
            interval_seconds: 15,
            enable_webhook: false,
            enable_logging: true,
            log_file: "resource_monitor.log".to_string(),
            cpu_warning_threshold: 80.0,
            tps_warning_threshold: 40.0,
            ram_warning_threshold: 1024.0,
        }
    }
}

impl Config {
    pub fn load_from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let path = path.as_ref();
        
        if !path.exists() {
            warn!("Configuration file not found at {:?}, creating default configuration", path);
            let default_config = Self::default();
            default_config.save_to_file(path)?;
            return Ok(default_config);
        }

        let content = fs::read_to_string(path)
            .with_context(|| format!("Failed to read configuration file: {:?}", path))?;

        let config: Config = serde_json::from_str(&content)
            .with_context(|| format!("Failed to parse configuration file: {:?}", path))?;

        info!("Configuration loaded successfully from {:?}", path);
        Ok(config)
    }

    pub fn save_to_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let path = path.as_ref();
        
        if let Some(parent) = path.parent() {
            fs::create_dir_all(parent)
                .with_context(|| format!("Failed to create directory: {:?}", parent))?;
        }

        let content = serde_json::to_string_pretty(self)
            .context("Failed to serialize configuration")?;

        fs::write(path, content)
            .with_context(|| format!("Failed to write configuration file: {:?}", path))?;

        info!("Configuration saved to {:?}", path);
        Ok(())
    }

    pub fn validate(&self) -> Result<()> {
        if self.enable_webhook && self.webhook_url.is_empty() {
            return Err(anyhow::anyhow!("Webhook is enabled but webhook_url is empty"));
        }

        if self.interval_seconds == 0 {
            return Err(anyhow::anyhow!("interval_seconds must be greater than 0"));
        }

        if self.cpu_warning_threshold < 0.0 || self.cpu_warning_threshold > 100.0 {
            return Err(anyhow::anyhow!("cpu_warning_threshold must be between 0 and 100"));
        }

        if self.tps_warning_threshold < 0.0 {
            return Err(anyhow::anyhow!("tps_warning_threshold must be greater than 0"));
        }

        if self.ram_warning_threshold < 0.0 {
            return Err(anyhow::anyhow!("ram_warning_threshold must be greater than 0"));
        }

        Ok(())
    }

    #[allow(dead_code)]
    pub fn reload_from_file<P: AsRef<Path>>(&mut self, path: P) -> Result<()> {
        let new_config = Self::load_from_file(path)?;
        new_config.validate()?;
        *self = new_config;
        info!("Configuration reloaded successfully");
        Ok(())
    }
}
