{"rustc": 17690870240741347250, "features": "[]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 16503403049695105087, "path": 10435173216431995975, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\hashbrown-ec8e043dddaf3a45\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}