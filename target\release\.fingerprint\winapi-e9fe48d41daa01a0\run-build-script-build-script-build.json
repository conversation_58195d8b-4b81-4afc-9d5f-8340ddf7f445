{"rustc": 17690870240741347250, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10020888071089587331, "build_script_build", false, 14076792929955562041]], "local": [{"RerunIfChanged": {"output": "release\\build\\winapi-e9fe48d41daa01a0\\output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "WINAPI_NO_BUNDLED_LIBRARIES", "val": null}}, {"RerunIfEnvChanged": {"var": "WINAPI_STATIC_NOBUNDLE", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}