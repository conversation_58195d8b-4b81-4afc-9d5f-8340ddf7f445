{"rustc": 17690870240741347250, "features": "[\"__tls\", \"default\", \"default-tls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"tokio-native-tls\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 16503403049695105087, "path": 9314193612922232225, "deps": [[40386456601120721, "percent_encoding", false, 11783629399837829715], [95042085696191081, "ipnet", false, 13960323621525990712], [264090853244900308, "sync_wrapper", false, 9385554530504679168], [784494742817713399, "tower_service", false, 5027250907489752967], [1906322745568073236, "pin_project_lite", false, 4871519555512407100], [3150220818285335163, "url", false, 1848195453442068703], [3722963349756955755, "once_cell", false, 14751525282124646153], [4405182208873388884, "http", false, 13380534260274165643], [5986029879202738730, "log", false, 10586697381578157762], [7414427314941361239, "hyper", false, 868219134036135106], [7620660491849607393, "futures_core", false, 9353799322333569453], [8405603588346937335, "winreg", false, 818599579698316648], [8569119365930580996, "serde_json", false, 16201051711568332704], [8915503303801890683, "http_body", false, 15897164279276544630], [9689903380558560274, "serde", false, 3065449639811988240], [10229185211513642314, "mime", false, 8995144885446109858], [10629569228670356391, "futures_util", false, 2502238988956157111], [12186126227181294540, "tokio_native_tls", false, 7896293956736373919], [12367227501898450486, "hyper_tls", false, 9495868001545556112], [12944427623413450645, "tokio", false, 7251993364916148229], [13763625454224483636, "h2", false, 3086765738545925840], [14564311161534545801, "encoding_rs", false, 602951504661302738], [16066129441945555748, "bytes", false, 4205761427962686373], [16311359161338405624, "rustls_pemfile", false, 13801223154244940721], [16542808166767769916, "serde_urlencoded", false, 18424739789062425815], [16785601910559813697, "native_tls_crate", false, 10737809100214092345], [18066890886671768183, "base64", false, 13342025178828945786]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-7f0dfb8ab8c85ec3\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}