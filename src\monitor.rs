use std::sync::Arc;
use std::time::{Duration, Instant};
use parking_lot::RwLock;
use sysinfo::System;
use tokio::time::interval;
use log::warn;
use serde::{Serialize, Deserialize};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ServerMetrics {
    pub cpu: f32,
    pub ram: f32,
    pub tps: f32,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl Default for ServerMetrics {
    fn default() -> Self {
        Self {
            cpu: 0.0,
            ram: 0.0,
            tps: 0.0,
            timestamp: chrono::Utc::now(),
        }
    }
}

pub struct ResourceMonitor {
    metrics: Arc<RwLock<ServerMetrics>>,
    tick_counter: Arc<RwLock<TickCounter>>,
    config: crate::config::Config,
}

#[derive(Debug)]
struct TickCounter {
    count: u64,
    last_reset: Instant,
    current_tps: f32,
}

impl TickCounter {
    fn new() -> Self {
        Self {
            count: 0,
            last_reset: Instant::now(),
            current_tps: 0.0,
        }
    }

    fn tick(&mut self) {
        self.count += 1;
        
        let elapsed = self.last_reset.elapsed();
        if elapsed >= Duration::from_secs(1) {
            self.current_tps = self.count as f32 / elapsed.as_secs_f32();
            self.count = 0;
            self.last_reset = Instant::now();
        }
    }

    fn get_tps(&self) -> f32 {
        self.current_tps
    }
}

impl ResourceMonitor {
    pub fn new(config: crate::config::Config) -> Self {
        Self {
            metrics: Arc::new(RwLock::new(ServerMetrics::default())),
            tick_counter: Arc::new(RwLock::new(TickCounter::new())),
            config,
        }
    }

    pub fn tick(&self) {
        self.tick_counter.write().tick();
    }

    pub fn start_monitoring(&mut self) -> Arc<RwLock<ServerMetrics>> {
        let metrics_clone = Arc::clone(&self.metrics);
        let tick_counter_clone = Arc::clone(&self.tick_counter);
        let config = self.config.clone();

        tokio::spawn(async move {
            let mut interval = interval(Duration::from_millis(1000)); // Update every second
            let mut system = System::new();

            loop {
                interval.tick().await;
                
                system.refresh_cpu();
                system.refresh_memory();

                let cpu_usage = system.cpus().iter()
                    .map(|cpu| cpu.cpu_usage())
                    .sum::<f32>() / system.cpus().len() as f32;

                let ram_usage = (system.used_memory() as f64 / 1024.0 / 1024.0) as f32;
                let tps = tick_counter_clone.read().get_tps();
                {
                    let mut metrics = metrics_clone.write();
                    metrics.cpu = cpu_usage;
                    metrics.ram = ram_usage;
                    metrics.tps = tps;
                    metrics.timestamp = chrono::Utc::now();
                }
                if cpu_usage > config.cpu_warning_threshold {
                    warn!("High CPU usage detected: {:.2}%", cpu_usage);
                }

                if tps < config.tps_warning_threshold && tps > 0.0 {
                    warn!("Low TPS detected: {:.2}", tps);
                }

                if ram_usage > config.ram_warning_threshold {
                    warn!("High RAM usage detected: {:.2}MB", ram_usage);
                }
            }
        });

        Arc::clone(&self.metrics)
    }
}
