@echo off
echo Building SA-MP Resource Monitor Plugin...
echo.

REM Check if Rust is installed
where cargo >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Error: Rust/Cargo not found. Please install Rust from https://rustup.rs/
    pause
    exit /b 1
)

REM Build for Windows (32-bit for SA-MP compatibility)
echo Building for Windows (i686-pc-windows-msvc)...
cargo build --release --target i686-pc-windows-msvc

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Build failed! Please check the error messages above.
    pause
    exit /b 1
)

REM Copy the built plugin to a more accessible location
if exist "target\i686-pc-windows-msvc\release\samp_resource_monitor.dll" (
    copy "target\i686-pc-windows-msvc\release\samp_resource_monitor.dll" "samp_resource_monitor.dll"
    echo.
    echo Build successful! Plugin saved as: samp_resource_monitor.dll
) else (
    echo.
    echo Warning: Plugin file not found at expected location.
    echo Check target\i686-pc-windows-msvc\release\ directory.
)

echo.
echo Installation instructions:
echo 1. Copy samp_resource_monitor.dll to your SA-MP server's plugins/ directory
echo 2. Add "samp_resource_monitor" to your server.cfg plugins line
echo 3. Copy config/resource_monitor.json to your server's config/ directory
echo 4. Copy resource_monitor.inc to your pawno/include/ directory
echo.
pause
