# SA-MP Resource Monitor Plugin

SA-MP plugin written in Rust that monitors server performance metrics (CPU, RAM, Tickrate) in real-time and provides both Pawn native functions and automatic webhook/WebSocket push capabilities.

## Installation

1. Download the compiled plugin for your platform:
   - Windows: `samp_resource_monitor.dll`
   - Linux: `samp_resource_monitor.so`

2. Place the plugin file in your SA-MP server's `plugins/` directory

3. Add the plugin to your `server.cfg`:
   ```
   plugins samp_resource_monitor
   ```

4. Create the configuration file `config/resource_monitor.json` (see Configuration section)

## Building from Source

### Prerequisites

- Rust toolchain (install from [rustup.rs](https://rustup.rs/))
- For Windows: MSVC toolchain or MinGW
- For Linux: GCC

### Build Commands

```bash
git clone https://github.com/amrulpxl/samp-resource-monitor
cd samp-resource-monitor
cargo build --release
```

## Configuration

Create `config/resource_monitor.json` in your SA-MP server directory:

```json
{
  "webhook_url": "https://discord.com/api/webhooks/YOUR_WEBHOOK_URL",
  "interval_seconds": 15,
  "enable_webhook": true,
  "enable_logging": true,
  "log_file": "resource_monitor.log",
  "cpu_warning_threshold": 80.0,
  "tps_warning_threshold": 40.0,
  "ram_warning_threshold": 1024.0
}
```

### Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `webhook_url` | string | `""` | Discord webhook URL or custom endpoint |
| `interval_seconds` | number | `15` | Seconds between automatic webhook pushes |
| `enable_webhook` | boolean | `false` | Enable/disable webhook functionality |
| `enable_logging` | boolean | `true` | Enable/disable file logging |
| `log_file` | string | `"resource_monitor.log"` | Log file path |
| `cpu_warning_threshold` | number | `80.0` | CPU usage warning threshold (%) |
| `tps_warning_threshold` | number | `40.0` | TPS warning threshold |
| `ram_warning_threshold` | number | `1024.0` | RAM usage warning threshold (MB) |

## Pawn Native Functions

### Basic Metrics

```pawn
// Get current CPU usage percentage (0.0 - 100.0)
native Float:GetServerCPU();

// Get current RAM usage in megabytes
native Float:GetServerRAM();

// Get current server tick rate (TPS)
native Float:GetTickRate();
```

### Advanced Functions

```pawn
// Get all metrics at once (more efficient)
native GetServerMetricsEx(&Float:cpu, &Float:ram, &Float:tps);

// Check if server is under heavy load
native bool:IsServerUnderLoad();

// Manually trigger webhook push
native bool:PushServerMetrics();

// Get server uptime in seconds
native GetServerUptimeSeconds();
```

## Usage Examples

### Basic Usage in Pawn

```pawn
#include <a_samp>

public OnGameModeInit()
{
    // Set a timer to check metrics every 30 seconds
    SetTimer("CheckServerMetrics", 30000, true);
    return 1;
}

forward CheckServerMetrics();
public CheckServerMetrics()
{
    new Float:cpu = GetServerCPU();
    new Float:ram = GetServerRAM();
    new Float:tps = GetTickRate();
    
    printf("Server Metrics - CPU: %.2f%%, RAM: %.2fMB, TPS: %.2f", cpu, ram, tps);
    
    // Check if server is under load
    if (IsServerUnderLoad())
    {
        printf("WARNING: Server is under heavy load!");
    }
    
    return 1;
}
```

### Advanced Usage with Manual Webhook

```pawn
public OnPlayerConnect(playerid)
{
    // Push metrics when player count changes
    if (GetPlayerCount() % 10 == 0) // Every 10 players
    {
        if (PushServerMetrics())
        {
            printf("Metrics pushed to webhook successfully");
        }
        else
        {
            printf("Failed to push metrics to webhook");
        }
    }
    return 1;
}

stock GetPlayerCount()
{
    new count = 0;
    for (new i = 0; i < MAX_PLAYERS; i++)
    {
        if (IsPlayerConnected(i)) count++;
    }
    return count;
}
```

### Performance Monitoring Command

```pawn
CMD:perf(playerid, params[])
{
    if (!IsPlayerAdmin(playerid)) return SendClientMessage(playerid, 0xFF0000FF, "Access denied!");
    
    new Float:cpu, Float:ram, Float:tps;
    GetServerMetricsEx(cpu, ram, tps);
    
    new string[128];
    format(string, sizeof(string), "Server Performance - CPU: %.2f%% | RAM: %.2fMB | TPS: %.2f", cpu, ram, tps);
    SendClientMessage(playerid, 0x00FF00FF, string);
    
    if (IsServerUnderLoad())
    {
        SendClientMessage(playerid, 0xFF0000FF, "WARNING: Server is currently under heavy load!");
    }
    
    return 1;
}
```

## Webhook Payload Format

The plugin sends JSON payloads to the configured webhook URL:

```json
{
  "cpu": 35.2,
  "ram": 128.4,
  "tps": 59.8,
  "timestamp": "2025-07-31T17:10:00Z"
}
```

### Discord Webhook Integration

For Discord webhooks, the plugin automatically formats the data as embeds with color coding:
- 🟢 Green: Normal performance
- 🟡 Yellow: Moderate load
- 🔴 Red: High load/warnings

## Logging

The plugin creates detailed logs in `resource_monitor.log`:

```
[2025-07-31 17:10:00 UTC] [INFO] SA-MP Resource Monitor Plugin loaded (v1.0.1)
[2025-07-31 17:10:01 UTC] [INFO] Initial metrics - CPU: 15.32%, RAM: 256.78MB, TPS: 64.00
[2025-07-31 17:10:15 UTC] [WARN] High CPU usage detected: 85.67%
[2025-07-31 17:10:30 UTC] [INFO] Periodic webhook sent successfully
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For issues and questions:
- Create an issue on GitHub.