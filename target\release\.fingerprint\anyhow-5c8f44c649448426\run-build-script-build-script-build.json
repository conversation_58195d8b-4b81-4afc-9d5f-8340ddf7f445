{"rustc": 17690870240741347250, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[13625485746686963219, "build_script_build", false, 4238791373578658969]], "local": [{"RerunIfChanged": {"output": "release\\build\\anyhow-5c8f44c649448426\\output", "paths": ["src/nightly.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}