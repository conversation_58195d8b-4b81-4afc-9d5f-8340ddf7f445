#include <a_samp>
#include <resource_monitor>
#include <zcmd>

#define PERF_CHECK_INTERVAL 30000 // 30 seconds
#define PERF_ALERT_INTERVAL 300000 // 5 minutes

new g_PerformanceTimer;
new g_LastAlertTime;

stock GetPerformanceStatus()
{
    new Float:cpu = GetServerCPU();
    new Float:ram = GetServerRAM();
    new Float:tps = GetTickRate();

    if (cpu > 90.0 || ram > 2048.0 || tps < 30.0)
        return 2; // CRITICAL

    if (cpu > 80.0 || ram > 1024.0 || tps < 40.0)
        return 1; // WARNING

    return 0; // GOOD
}

stock GetDetailedPerformanceString(string[], size = sizeof(string))
{
    new Float:cpu, Float:ram, Float:tps;
    GetServerMetricsEx(cpu, ram, tps);

    new status_str[16];
    switch (GetPerformanceStatus())
    {
        case 0: format(status_str, sizeof(status_str), "Good");
        case 1: format(status_str, sizeof(status_str), "Warning");
        case 2: format(status_str, sizeof(status_str), "Critical");
        default: format(status_str, sizeof(status_str), "Unknown");
    }

    format(string, size,
        "Status: %s | CPU: %.1f%% | RAM: %.1fMB | TPS: %.1f",
        status_str, cpu, ram, tps);
    return 1;
}

stock StartPerformanceMonitoring(interval_ms = 30000)
{
    return SetTimer("CheckPerformanceMetrics", interval_ms, true);
}

public OnGameModeInit()
{
    print("\n----------------------------------");
    print(" SA-MP Resource Monitor ");
    print("----------------------------------\n");
    
    g_PerformanceTimer = StartPerformanceMonitoring(PERF_CHECK_INTERVAL);
    g_LastAlertTime = 0;
    
    LogInitialMetrics();
    
    return 1;
}

public OnGameModeExit()
{
    if (g_PerformanceTimer != -1)
    {
        KillTimer(g_PerformanceTimer);
    }
    
    print("Resource Monitor Example: Gamemode exited");
    return 1;
}

public OnPlayerConnect(playerid)
{
    new name[MAX_PLAYER_NAME];
    GetPlayerName(playerid, name, sizeof(name));
    printf("Player %s connected. Current player count: %d", name, GetOnlinePlayerCount());
    
    new player_count = GetOnlinePlayerCount();
    if (player_count % 5 == 0) 
    {
        if (PushServerMetrics())
        {
            printf("Metrics pushed to webhook (player milestone: %d)", player_count);
        }
    }
    
    return 1;
}

public OnPlayerDisconnect(playerid, reason)
{
    new name[MAX_PLAYER_NAME];
    GetPlayerName(playerid, name, sizeof(name));
    printf("Player %s disconnected. Current player count: %d", name, GetOnlinePlayerCount() - 1);
    
    return 1;
}

forward CheckPerformanceMetrics();
public CheckPerformanceMetrics()
{
    new Float:cpu, Float:ram, Float:tps;
    GetServerMetricsEx(cpu, ram, tps);
    
    new status = GetPerformanceStatus();
    printf("Performance Check - CPU: %.1f%%, RAM: %.1fMB, TPS: %.1f", cpu, ram, tps);
    
    if (status != PERF_STATUS_GOOD)
    {
        new current_time = gettime();
        if (current_time - g_LastAlertTime >= (PERF_ALERT_INTERVAL / 1000))
        {
            new perf_string[128];
            GetDetailedPerformanceString(perf_string);
            
            printf("PERFORMANCE ALERT: %s", perf_string);
            
            SendPerformanceAlertToAdmins(perf_string);
            PushServerMetrics();
            
            g_LastAlertTime = current_time;
        }
    }
    
    return 1;
}

CMD:perf(playerid, params[])
{
    new perf_string[128];
    GetDetailedPerformanceString(perf_string);
    
    new message[144];
    format(message, sizeof(message), "Server Performance: %s", perf_string);
    SendClientMessage(playerid, 0x00FF00FF, message);
    
    return 1;
}

CMD:pushmetrics(playerid, params[])
{  
    if (PushServerMetrics())
    {
        SendClientMessage(playerid, 0x00FF00FF, "Metrics pushed to webhook successfully.");
    }
    else
    {
        SendClientMessage(playerid, 0xFF0000FF, "Failed to push metrics to webhook.");
    }
    
    return 1;
}

CMD:serverstats(playerid, params[])
{
    new Float:cpu = GetServerCPU();
    new Float:ram = GetServerRAM();
    new Float:tps = GetTickRate();
    new uptime = GetServerUptimeSeconds();
    new player_count = GetOnlinePlayerCount();
    
    new message[256];
    format(message, sizeof(message), 
        "Server Statistics:\n"
        "CPU Usage: %.1f%%\n"
        "RAM Usage: %.1fMB\n"
        "Tick Rate: %.1f TPS\n"
        "Uptime: %d seconds\n"
        "Players Online: %d",
        cpu, ram, tps, uptime, player_count);
    
    ShowPlayerDialog(playerid, 1000, DIALOG_STYLE_MSGBOX, "Server Statistics", message, "OK", "");
    
    return 1;
}

stock LogInitialMetrics()
{
    new Float:cpu, Float:ram, Float:tps;
    GetServerMetricsEx(cpu, ram, tps);
    
    printf("Initial Server Metrics:");
    printf("  CPU Usage: %.2f%%", cpu);
    printf("  RAM Usage: %.2f MB", ram);
    printf("  Tick Rate: %.2f TPS", tps);
    
    if (IsServerUnderLoad())
    {
        print("  WARNING: Server is currently under load!");
    }
    else
    {
        print("  Server performance is normal.");
    }
}

stock GetOnlinePlayerCount()
{
    new count = 0;
    for (new i = 0; i < MAX_PLAYERS; i++)
    {
        if (IsPlayerConnected(i)) count++;
    }
    return count;
}

stock SendPerformanceAlertToAdmins(const message[])
{
    new alert_msg[144];
    format(alert_msg, sizeof(alert_msg), "[PERFORMANCE ALERT] %s", message);
    
    for (new i = 0; i < MAX_PLAYERS; i++)
    {
        if (IsPlayerConnected(i) && IsPlayerAdmin(i))
        {
            SendClientMessage(i, 0xFF6600FF, alert_msg);
        }
    }
}
